import { Overlay, OverlayType } from '@clipnest/remotion-shared/types'
import {
  byStartFrame,
  findLastOverlay,
  findOverlay,
  findStoryboardIndex,
  getOverlayTimeRange,
  getOverlayTrackIndex
} from '@/modules/video-editor/utils/overlay-helper'
import { IndexableOverlay, Track, TrackType } from '@/modules/video-editor/types'
import { toast } from 'react-toastify'
import { DEFAULT_OVERLAY } from '@/modules/video-editor/constants'
import { cloneDeep } from 'lodash'

type AddOverlayPayload = {}
  & Pick<Partial<Overlay>, keyof typeof DEFAULT_OVERLAY>
  & Omit<Overlay, keyof typeof DEFAULT_OVERLAY | 'id'>

export type SingleOverlayUpdatePayload = Overlay & { targetTrackIndex?: number }

export const MAP_OVERLAY_TO_TRACK: Record<OverlayType, TrackType> = {
  [OverlayType.STORYBOARD]: TrackType.STORYBOARD,
  [OverlayType.TEXT]: TrackType.TEXT,
  [OverlayType.VIDEO]: TrackType.VIDEO,
  [OverlayType.SOUND]: TrackType.SOUND,
  [OverlayType.STICKER]: TrackType.IMAGE,
  [OverlayType.CAPTION]: TrackType.TEXT,
}

/**
 * 检查 Overlay 是否可以添加到指定的 Track
 */
export function isOverlayAcceptableByTrack(overlay: Pick<Overlay, 'type'>, track: Pick<Track, 'type'>): boolean {
  if (overlay.type === OverlayType.STORYBOARD) {
    return track.type === TrackType.STORYBOARD
  }

  if (track.type === TrackType.MIXED) return true

  if (track.type === TrackType.NARRATION) {
    return overlay.type === OverlayType.SOUND || overlay.type === OverlayType.TEXT
  }

  return MAP_OVERLAY_TO_TRACK[overlay.type] === track.type
}

/**
 * 根据起始帧查找所在分镜
 */
export function findStoryboardByFromFrame(
  tracks: Track[],
  fromFrame: number
): IndexableOverlay | null {
  const storyboards = tracks
    .find(o => o.type === TrackType.STORYBOARD)!
    .overlays
    .filter(o => o.type === OverlayType.STORYBOARD)
    .sort(byStartFrame())

  if (!storyboards) {
    return null
  }

  let endFrame = 0
  for (let index = 0; index < storyboards.length; index++) {
    const storyboard = storyboards[index]
    endFrame += storyboard.durationInFrames

    if (endFrame > fromFrame) {
      return {
        ...storyboard,
        index,
      }
    }
  }

  // 找不到时, 说明目标帧超出最后一个分镜的终止帧, 则默认返回最后一个分镜
  return {
    ...storyboards.at(-1)!,
    index: storyboards.length - 1,
  }
}

/**
 * 生成新的 Overlay ID
 */
export function generateNewOverlayId(tracks: Track[]) {
  const overlays = tracks.map(t => t.overlays).flat()
  if (!overlays.length) return 1

  return Math.max(...overlays.map(o => o.id)) + 1
}

/**
 * 获取轨道类型对应的中文标签
 * @param trackType 轨道类型
 * @returns 中文标签
 */
export function getTrackTypeLabel(trackType: TrackType): string {
  switch (trackType) {
    case TrackType.VIDEO:
      return '视频'
    case TrackType.SOUND:
      return '音频'
    case TrackType.TEXT:
      return '文本'
    case TrackType.IMAGE:
      return '图片'
    case TrackType.MIXED:
      return '混合'
    case TrackType.NARRATION:
      return '口播'
    case TrackType.STORYBOARD:
      return '分镜'
    default:
      return '内容'
  }
}

/**
 * 确保指定轨道类型的末尾包含且仅包含一条空轨道，并按规则排序
 */
export function ensureEmptyTracksAndSort(tracks: Track[]): Track[] {
  /**
   * 轨道类型的唯一标识符，用于区分不同的轨道类型组合
   */
  type TrackTypeKey = string

  /**
   * 获取轨道类型的排序优先级
   */
  const _getTrackTypePriority = (type: TrackType): number => {
    switch (type) {
      case TrackType.STORYBOARD:
        return 0
      case TrackType.VIDEO:
        return 1
      case TrackType.NARRATION:
        return 2
      case TrackType.TEXT:
        return 3
      case TrackType.IMAGE:
        return 4
      case TrackType.SOUND:
        return 5
      case TrackType.MIXED:
        return 6
      default:
        return 7
    }
  }

  // 初始化统计信息
  const _initializeTrackTypeStats = () => {
    [
      TrackType.VIDEO,
      TrackType.NARRATION,
      TrackType.MIXED,
    ].forEach(type => {
      trackTypeStats.set(_getTrackTypeKey({ type, isGlobalTrack: false }), { lastTrackEmpty: false, lastIndex: -1 })
    });

    [
      TrackType.TEXT,
      TrackType.SOUND,
      TrackType.IMAGE,
      TrackType.MIXED
    ].forEach(type => {
      trackTypeStats.set(_getTrackTypeKey({ type, isGlobalTrack: true }), { lastTrackEmpty: false, lastIndex: -1 })
    })
  }

  /**
   * 生成轨道类型的唯一标识符
   */
  const _getTrackTypeKey = (track: Pick<Track, 'type' | 'isGlobalTrack'>): TrackTypeKey => `${track.type}-${track.isGlobalTrack ? 'global' : 'local'}`

  const result: Track[] = [...tracks]

  // 统计每种轨道类型的情况
  const trackTypeStats = new Map<TrackTypeKey, { lastTrackEmpty: boolean; lastIndex: number }>()
  _initializeTrackTypeStats()

  // 遍历轨道，更新统计信息
  result.forEach((track, index) => {
    if (track.type === TrackType.STORYBOARD) return

    const key = _getTrackTypeKey(track)
    const stats = trackTypeStats.get(key)
    if (stats) {
      stats.lastIndex = index
      stats.lastTrackEmpty = track.overlays.length === 0
    }
  })

  // 为需要的轨道类型添加空轨道
  const tracksToAdd: { track: Track; insertAfterIndex: number }[] = []

  trackTypeStats.forEach((stats, typeKey) => {
    // 如果该类型没有空轨道，需要添加一条
    if (!stats.lastTrackEmpty) {
      const [type, isGlobalStr] = typeKey.split('-')
      const isGlobalTrack = isGlobalStr === 'global'

      const emptyTrack: Track = {
        type: type as TrackType,
        isGlobalTrack,
        overlays: []
      }

      // 将空轨道添加到该类型的最后一个轨道之后
      tracksToAdd.push({
        track: emptyTrack,
        insertAfterIndex: stats.lastIndex
      })
    }
  })

  // 按插入位置从后往前排序，避免插入时索引偏移
  tracksToAdd.sort((a, b) => b.insertAfterIndex - a.insertAfterIndex)

  // 插入空轨道
  tracksToAdd.forEach(({ track, insertAfterIndex }) => {
    result.splice(insertAfterIndex + 1, 0, track)
  })

  // 5. 删除每种轨道类型组末尾多余的连续空轨道，只保留最后一条
  const tracksToRemove: number[] = []

  // 重新统计轨道类型信息（因为可能添加了新轨道）
  const updatedTrackTypeStats = new Map<TrackTypeKey, { indices: number[] }>()
  _initializeTrackTypeStats()

  // 收集每种类型的轨道索引
  result.forEach((track, index) => {
    if (track.type === TrackType.STORYBOARD) return

    const key = _getTrackTypeKey(track)
    const stats = updatedTrackTypeStats.get(key)
    if (stats) {
      stats.indices.push(index)
    }
  })

  // 对每种轨道类型，检查末尾的连续空轨道
  updatedTrackTypeStats.forEach(stats => {
    if (stats.indices.length <= 1) return // 只有一条或没有轨道，无需处理

    const indices = stats.indices
    let consecutiveEmptyCount = 0

    // 从该类型的最后一个轨道开始向前遍历，统计末尾连续空轨道数量
    for (let i = indices.length - 1; i >= 0; i--) {
      const trackIndex = indices[i]
      const track = result[trackIndex]

      if (track.overlays.length === 0) {
        consecutiveEmptyCount++
      } else {
        break // 遇到非空轨道，停止统计
      }
    }

    // 如果末尾有多条连续空轨道，标记除最后一条外的所有空轨道为待删除
    if (consecutiveEmptyCount > 1) {
      for (let i = 0; i < consecutiveEmptyCount - 1; i++) {
        const trackIndexToRemove = indices[indices.length - 1 - consecutiveEmptyCount + 1 + i]
        tracksToRemove.push(trackIndexToRemove)
      }
    }
  })

  // 按索引从大到小排序，避免删除时索引偏移
  tracksToRemove.sort((a, b) => b - a)

  // 删除多余的空轨道
  tracksToRemove.forEach(index => {
    result.splice(index, 1)
  })

  if (!result.some(o => o.type === TrackType.STORYBOARD)) {
    result.push({
      type: TrackType.STORYBOARD,
      overlays: [],
    })
  }

  // 6. 最终排序（只按基本规则排序，不调整空轨道位置）
  return result.sort((a, b) => {
    // 最高优先级：分镜轨道永远在最前面
    if (a.type === TrackType.STORYBOARD && b.type !== TrackType.STORYBOARD) return -1
    if (a.type !== TrackType.STORYBOARD && b.type === TrackType.STORYBOARD) return 1

    // 第一优先级：isGlobalTrack（非全局在前，全局在后）
    const aIsGlobal = a.isGlobalTrack ?? false
    const bIsGlobal = b.isGlobalTrack ?? false
    if (aIsGlobal !== bIsGlobal) {
      return aIsGlobal ? 1 : -1
    }

    // 第二优先级：type（按优先级排序）
    const aPriority = _getTrackTypePriority(a.type)
    const bPriority = _getTrackTypePriority(b.type)
    if (aPriority !== bPriority) {
      return aPriority - bPriority
    }

    return 0
  })
}

export function calculateTrackAfterOverlayPushed(
  tracks: Track[],
  trackIndex: number,
  payload: Omit<AddOverlayPayload, 'from'>,
  desiredStartFrame?: number
): [Track, Overlay | null] {
  const decideTargetPosition = (
    tracks: Track[],
    currentTrack: Track
  ): { startFrame: number, storyboardIndex?: number } => {
    if (desiredStartFrame === undefined) {
      const [, lastOverlayEnd] = getOverlayTimeRange(findLastOverlay(currentTrack.overlays))
      return {
        startFrame: lastOverlayEnd
      }
    }

    const storyboard = !currentTrack.isGlobalTrack
      ? findStoryboardByFromFrame(tracks, desiredStartFrame)
      : null

    if (!storyboard) return { startFrame: desiredStartFrame }

    const overlaysInStoryboard = currentTrack.overlays.filter(o => o.storyboardIndex === storyboard.index)

    const maxEndFrame = overlaysInStoryboard.reduce(
      (max, overlay) => Math.max(max, overlay.from + overlay.durationInFrames),
      storyboard.from
    )

    return { startFrame: maxEndFrame, storyboardIndex: storyboard.index }
  }

  const track = tracks[trackIndex]

  if (!isOverlayAcceptableByTrack(payload, track)) {
    toast('目标轨道不支持该类型的素材', { type: 'error' })
    return [track, null]
  }

  const { startFrame, storyboardIndex } = decideTargetPosition(tracks, track)

  const newOverlay = {
    ...DEFAULT_OVERLAY,
    ...payload,
    id: generateNewOverlayId(tracks),
    from: startFrame,
    ...(storyboardIndex !== undefined && { storyboardIndex })
  } as Overlay

  const newTrack = {
    ...track,
    overlays: [...track.overlays, newOverlay]
  }

  return [newTrack, newOverlay]
}

export function calculateTrackAfterOverlayRemoved(
  tracks: Track[],
  trackIndex: number,
  removed: Overlay
): Track {
  const track = tracks[trackIndex]
  const isRemovingStoryboard = removed.type === OverlayType.STORYBOARD

  // 删除分镜的时候
  if (isRemovingStoryboard) {
    // 1. 删除分镜轨道中的对应分镜
    if (track.type === TrackType.STORYBOARD) {
      return {
        ...track,
        overlays: track.overlays
          .filter(o => o.id !== removed.id)
          .map(overlay => {
            if (overlay.from > removed.from) {
              return {
                ...overlay,
                from: overlay.from - removed.durationInFrames
              }
            }

            return overlay
          })
      }
    }

    // 2. 删除混剪轨道中，属于该分镜的所有 Overlay，同时前移后方的 Overlay
    const removedStoryboardIndex = findStoryboardIndex(tracks, removed)
    if (!track.isGlobalTrack) {
      return {
        ...track,
        overlays: track.overlays
          .filter(o => o.storyboardIndex !== removedStoryboardIndex)
          .map(o => {
            if (o.storyboardIndex !== undefined && o.storyboardIndex > removedStoryboardIndex) {
              return {
                ...o,
                from: o.from - removed.durationInFrames,
                storyboardIndex: o.storyboardIndex - 1
              }
            }

            return o
          })
      }
    }
  }

  const overlayIndex = track.overlays.findIndex(o => o.id === removed.id)

  if (overlayIndex !== -1) {
    return {
      ...track,
      overlays: track.overlays
        .filter((_, index) => index !== overlayIndex)
        .map(overlay => {
          if (track.isGlobalTrack || track.type !== TrackType.VIDEO) return overlay
          if (overlay.from > removed.from && overlay.storyboardIndex === removed.storyboardIndex) {
            return {
              ...overlay,
              from: overlay.from - removed.durationInFrames
            }
          }

          return overlay
        })
    }
  }

  return track
}

export function calculateTracksAfterOverlayUpdated(
  tracks: Track[],
  updatedOverlay: SingleOverlayUpdatePayload
) {
  if (updatedOverlay.type === OverlayType.STORYBOARD) {
    updatedOverlay.storyboardIndex = undefined
  }

  const { id: targetId } = updatedOverlay

  const targetOverlay = findOverlay(tracks, targetId)
  const sourceTrackIndex = getOverlayTrackIndex(tracks, targetId)

  // 如果找不到 overlay 则跳过
  if (sourceTrackIndex === -1 || !targetOverlay) return tracks

  const cloned = cloneDeep(tracks)
  const originalTrackIndex = getOverlayTrackIndex(tracks, targetId)
  const { targetTrackIndex = originalTrackIndex } = updatedOverlay

  // 如果行号没有改变，就地更新
  if (originalTrackIndex === targetTrackIndex) {
    cloned[sourceTrackIndex] = {
      ...cloned[sourceTrackIndex],
      overlays: cloned[sourceTrackIndex].overlays.map(o =>
        o.id === targetId ? updatedOverlay : o
      )
    }
  } else {
    // 从源 track 中移除
    cloned[sourceTrackIndex] = {
      ...cloned[sourceTrackIndex],
      overlays: tracks[sourceTrackIndex].overlays.filter(o => o.id !== updatedOverlay.id)
    }

    // 添加到目标 track
    cloned[targetTrackIndex] = {
      ...cloned[targetTrackIndex],
      overlays: [...cloned[targetTrackIndex].overlays, updatedOverlay]
    }
  }

  return cloned
}
