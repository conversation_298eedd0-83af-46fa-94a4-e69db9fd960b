import { useCallback } from 'react'
import { Overlay } from '@clipnest/remotion-shared/types'
import { findOverlayStoryboard, findTrackByOverlay } from '@/modules/video-editor/utils/overlay-helper'
import { useEditorContext } from '../editor/context'
import { findStoryboardByFromFrame, isOverlayAcceptableByTrack } from '@/modules/video-editor/utils/track-helper'
import { IndexableTrack, Track } from '../../types'
import { DraggableState, OverlayDragInfo, useDragContext } from './drag.context'
import { snapToGrid } from './drag.utils'
import { useTimelineContext } from '../timeline/context'
import { AdjustCalculator } from '@/modules/video-editor/contexts/drag/adjust-calculator'
import { PIXELS_PER_FRAME } from '@/modules/video-editor/constants'
import { clamp } from 'lodash'

function calculateDraggableState(
  tracks: Track[],
  currentOverlay: Overlay,
  intendedNewFrom: number,
  targetTrack: IndexableTrack,
): DraggableState {
  if (!currentOverlay || !isOverlayAcceptableByTrack(currentOverlay, targetTrack)) {
    return {
      draggable: false,
    }
  }

  const originalTrack = findTrackByOverlay(tracks, currentOverlay.id) || null

  const calculator = new AdjustCalculator(tracks)
  const targetStoryboard = targetTrack.isGlobalTrack
    ? null
    : findStoryboardByFromFrame(tracks, intendedNewFrom)

  return calculator.calcAdjustForMoving(
    currentOverlay,
    findOverlayStoryboard(tracks, currentOverlay),
    originalTrack,
    targetStoryboard,
    targetTrack,
    intendedNewFrom,
  )
}

/**
 * 处理 TimelineItem 在时间轴上拖动
 */
export const useTimelineItemMoving = () => {
  const { zoomScale } = useTimelineContext()
  const { tracks, durationInFrames } = useEditorContext()
  const { dragInfoRef, updateDraggableState } = useDragContext()

  const calculatePositionInfo = useCallback((
    dragInfo: OverlayDragInfo,
    deltaX: number,
    targetTrackIndex?: number
  ) => {
    const deltaFrame = snapToGrid(deltaX / zoomScale / PIXELS_PER_FRAME)

    // 使用传入的 targetTrackIndex，如果没有则使用初始轨道
    const targetRow = targetTrackIndex !== undefined
      ? clamp(targetTrackIndex, 0, tracks.length - 1)
      : dragInfo.initialRow

    const targetStartFrame: number = Math.max(0, dragInfo.initialFrom + deltaFrame)

    return {
      targetRow,
      targetStartFrame,
    }
  }, [durationInFrames, zoomScale, tracks.length])

  const handleOverlayDragMove = useCallback(
    (deltaX: number, targetTrackIndex: number) => {
      if (!dragInfoRef.current) return

      const { targetRow, targetStartFrame } = calculatePositionInfo(
        dragInfoRef.current,
        deltaX,
        targetTrackIndex
      )

      dragInfoRef.current.currentRow = targetRow

      updateDraggableState(
        calculateDraggableState(
          tracks,
          dragInfoRef.current.overlay,
          targetStartFrame,
          { ...tracks[targetRow], index: targetRow },
        )
      )
    },
    [calculatePositionInfo, tracks],
  )

  return {
    handleOverlayDragMove,
  }
}
