import pkg from './package.json' with {type: 'json'}
import mapWorkspaces from '@npmcli/map-workspaces'
import { join } from 'node:path'
import { pathToFileURL } from 'node:url'
import process from 'node:process'

// 获取当前的发布渠道
function getDistributionChannel() {
  // 优先使用环境变量
  if (process.env.VITE_DISTRIBUTION_CHANNEL) {
    return process.env.VITE_DISTRIBUTION_CHANNEL
  }

  // 从版本号推断渠道
  const version = pkg.version
  if (version.includes('-alpha')) return 'alpha'
  if (version.includes('-beta')) return 'beta'
  if (version.includes('-dev')) return 'dev'

  return 'latest'
}

// 获取平台特定的发布配置
function getPlatformPublishConfig() {
  const channel = getDistributionChannel()
  const baseUrl = 'https://dev-common-**********.cos.ap-guangzhou.myqcloud.com'

  return [
    {
      provider: 'generic',
      url: `${baseUrl}/clipnest-update/${channel}/latest/`,
      channel: channel
    }
  ]
}

export default /** @type import('electron-builder').Configuration */
({
  appId: 'com.gaolingtech.clipnest',
  productName: 'clipnest',
  copyright: 'Copyright © 2024 Gaoling Tech',
  directories: {
    output: 'dist',
    buildResources: 'buildResources',
  },
  generateUpdatesFilesForAllChannels: false,
  extraResources: [
    {
      from: 'packages/workers/dist',
      to: 'workers',
      filter: ['**/*']
    }
  ],
  /**
     * It is recommended to avoid using non-standard characters such as spaces in artifact names,
     * as they can unpredictably change during deployment, making them impossible to locate and download for update.
     */
  artifactName: '${productName}-${version}-${os}-${arch}.${ext}',
  publish: getPlatformPublishConfig(),
  files: [
    'LICENSE*',
    pkg.main,
    '!node_modules/@app/**',
    ...await getListOfFilesFromEachWorkspace(),
  ],
  win: {
    target: [
      {
        target: 'nsis',
        arch: ['x64']  // 只构建 64位版本
      }
    ],
    verifyUpdateCodeSignature: false,
    legalTrademarks: 'Copyright © 2024 Gaoling Tech',
    requestedExecutionLevel: 'asInvoker',
  },
  mac: {
    target: [
      {
        target: 'dmg',
        arch: ['x64', 'arm64']
      }
    ],
    category: 'public.app-category.productivity',
    icon: 'buildResources/icon.icns',
    darkModeSupport: true,
    hardenedRuntime: true,
    gatekeeperAssess: false,
    entitlements: 'buildResources/entitlements.mac.plist',
    entitlementsInherit: 'buildResources/entitlements.mac.plist',
  },
  dmg: {
    title: '${productName} ${version}',
    icon: 'buildResources/icon.icns',
    background: 'buildResources/dmg-background.png',
    contents: [
      {
        x: 130,
        y: 220
      },
      {
        x: 410,
        y: 220,
        type: 'link',
        path: '/Applications'
      }
    ],
    window: {
      width: 540,
      height: 380
    }
  },
  linux: {
    target: [
      {
        target: 'deb',
        arch: ['x64']
      },
      {
        target: 'AppImage',
        arch: ['x64']
      }
    ],
    category: 'Utility',
    icon: 'buildResources/icon.png',
    synopsis: 'A powerful clipboard manager',
    description: 'ClipNest is a feature-rich clipboard manager that helps you organize and manage your clipboard history efficiently.',
    vendor: 'Gaoling Tech',
    maintainer: 'Gaoling Tech <<EMAIL>>',
  },
  deb: {
    priority: 'optional',
    depends: ['gconf2', 'gconf-service', 'libnotify4', 'libappindicator1', 'libxtst6', 'libnss3'],
  },
  nsis: {
    oneClick: false,
    allowToChangeInstallationDirectory: true,
    allowElevation: true,
    createDesktopShortcut: 'always',
    createStartMenuShortcut: true,
    shortcutName: 'ClipNest',
    menuCategory: false,
    runAfterFinish: false
  }
})

/**
 * By default, electron-builder copies each package into the output compilation entirety,
 * including the source code, tests, configuration, assets, and any other files.
 *
 * So you may get compiled app structure like this:
 * ```
 * app/
 * ├── node_modules/
 * │   └── workspace-packages/
 * │       ├── package-a/
 * │       │   ├── src/            # Garbage. May be safely removed
 * │       │   ├── dist/
 * │       │   │   └── index.js    # Runtime code
 * │       │   ├── vite.config.js  # Garbage
 * │       │   ├── .env            # some sensitive config
 * │       │   └── package.json
 * │       ├── package-b/
 * │       ├── package-c/
 * │       └── package-d/
 * ├── packages/
 * │   └── entry-point.js
 * └── package.json
 * ```
 *
 * To prevent this, we read the "files"
 * property from each package's package.json
 * and add all files that do not match the patterns to the exclusion list.
 *
 * This way,
 * each package independently determines which files will be included in the final compilation and which will not.
 *
 * So if `package-a` in its `package.json` describes
 * ```json
 * {
 *   "name": "package-a",
 *   "files": [
 *     "dist/**\/"
 *   ]
 * }
 * ```
 *
 * Then in the compilation only those files and `package.json` will be included:
 * ```
 * app/
 * ├── node_modules/
 * │   └── workspace-packages/
 * │       ├── package-a/
 * │       │   ├── dist/
 * │       │   │   └── index.js    # Runtime code
 * │       │   └── package.json
 * │       ├── package-b/
 * │       ├── package-c/
 * │       └── package-d/
 * ├── packages/
 * │   └── entry-point.js
 * └── package.json
 * ```
 */
async function getListOfFilesFromEachWorkspace() {

  /**
   * @type {Map<string, string>}
   */
  const workspaces = await mapWorkspaces({
    cwd: process.cwd(),
    pkg,
  })

  const allFilesToInclude = []

  for (const [name, path] of workspaces) {
    // 跳过 workers 包，因为它通过 extraResources 单独处理
    if (name === '@app/workers') {
      continue
    }

    const pkgPath = join(path, 'package.json')
    const { default: workspacePkg } = await import(pathToFileURL(pkgPath), { with: { type: 'json' } })

    let patterns = workspacePkg.files || ['dist/**', 'package.json']

    patterns = patterns.map(p => join('node_modules', name, p))
    allFilesToInclude.push(...patterns)
  }

  return allFilesToInclude
}
