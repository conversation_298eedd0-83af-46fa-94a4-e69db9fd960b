import React, { useRef, useState } from 'react'
import { useTimelineClipboard } from '@/modules/video-editor/contexts/timeline/useTimelineClipboard'
import { useTimelineZoom } from '@/modules/video-editor/contexts/timeline/useTimelineZoom'
import { useTimelineOverlayActivation } from '@/modules/video-editor/contexts/timeline/useTimelineOverlayActivation'
import { useTimelineTracksLayout } from '@/modules/video-editor/contexts/timeline/useTimelineTracksLayout'
import { TimelineContext } from '@/modules/video-editor/contexts'

export const TimelineProvider: React.FC<{ children: React.ReactNode }> = ({
  children
}) => {
  const [isContextMenuOpen, setIsContextMenuOpen] = useState(false)

  const timelineGridRef = useRef<HTMLDivElement>(null)

  const clipboard = useTimelineClipboard()
  const layout = useTimelineTracksLayout()
  const zoom = useTimelineZoom(timelineGridRef)
  const overlaySelection = useTimelineOverlayActivation()

  return (
    <TimelineContext.Provider
      value={{
        ...zoom,
        ...overlaySelection,
        clipboard,
        layout,

        isContextMenuOpen,
        setIsContextMenuOpen,

        timelineGridRef,
      }}
    >
      {children}
    </TimelineContext.Provider>
  )
}
