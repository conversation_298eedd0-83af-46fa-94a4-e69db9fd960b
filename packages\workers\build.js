import { existsSync, mkdirSync, copyFileSync } from 'fs'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'
import { execSync } from 'child_process'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

console.log('🔨 Building upload worker...')

/**
 * 安装 Worker 依赖
 */
function installWorkerDependencies() {
  console.log('📦 Installing worker dependencies...')

  try {
    // 在 workers 目录下独立安装依赖，跳过 postinstall 脚本（避免 electron-rebuild）
    execSync('npm install --production --ignore-scripts', {
      cwd: __dirname,
      stdio: 'inherit'
    })
    console.log('✅ Worker dependencies installed successfully')
  } catch (error) {
    console.error('❌ Failed to install worker dependencies:', error.message)
    throw error
  }
}

try {
  const distDir = join(__dirname, 'dist')
  if (!existsSync(distDir)) {
    mkdirSync(distDir, { recursive: true })
  }

  // 首先安装依赖
  installWorkerDependencies()

  console.log('📄 Copying compiled worker files...')
  const sourceWorkerPath = join(__dirname, '../main/dist/upload-worker.js')
  const targetWorkerPath = join(distDir, 'upload-worker.js')

  if (!existsSync(sourceWorkerPath)) {
    throw new Error(`Compiled worker file not found: ${sourceWorkerPath}. Please run main build first.`)
  }

  copyFileSync(sourceWorkerPath, targetWorkerPath)

  copyFileSync(
    join(__dirname, 'package.json'),
    join(distDir, 'package.json')
  )

  console.log('✅ Upload worker build completed!')
  console.log(`📁 Output: ${distDir}`)
  console.log(`📄 Worker file: ${targetWorkerPath}`)
} catch (error) {
  console.error('❌ Build failed:', error.message)
  process.exit(1)
}
