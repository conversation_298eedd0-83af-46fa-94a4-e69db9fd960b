import { existsSync, mkdirSync, copyFileSync, readFileSync, readdirSync } from 'fs'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'
import { execSync } from 'child_process'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

console.log('🔨 Building upload worker...')

/**
 * 复制依赖包及其所有子依赖
 * @param {string} packageName 包名
 * @param {string} sourceNodeModules 源 node_modules 路径
 * @param {string} targetNodeModules 目标 node_modules 路径
 * @param {Set<string>} copiedPackages 已复制的包集合，避免重复复制
 */
function copyPackageWithDependencies(packageName, sourceNodeModules, targetNodeModules, copiedPackages = new Set()) {
  if (copiedPackages.has(packageName)) {
    return
  }

  const sourcePackagePath = join(sourceNodeModules, packageName)
  const targetPackagePath = join(targetNodeModules, packageName)

  if (!existsSync(sourcePackagePath)) {
    console.warn(`⚠️ Package not found: ${packageName}`)
    return
  }

  console.log(`📦 Copying package: ${packageName}`)

  // 复制整个包目录
  try {
    execSync(`xcopy "${sourcePackagePath}" "${targetPackagePath}" /E /I /Y /Q`, { stdio: 'pipe' })
  } catch (error) {
    console.warn(`⚠️ Failed to copy ${packageName}:`, error.message)
    return
  }

  copiedPackages.add(packageName)

  // 读取包的 package.json 获取依赖
  const packageJsonPath = join(sourcePackagePath, 'package.json')
  if (existsSync(packageJsonPath)) {
    try {
      const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf8'))
      const dependencies = {
        ...packageJson.dependencies,
        ...packageJson.peerDependencies
      }

      // 递归复制依赖
      for (const depName of Object.keys(dependencies || {})) {
        copyPackageWithDependencies(depName, sourceNodeModules, targetNodeModules, copiedPackages)
      }
    } catch (error) {
      console.warn(`⚠️ Failed to read package.json for ${packageName}:`, error.message)
    }
  }
}

/**
 * 安装 Worker 依赖
 */
function installWorkerDependencies() {
  console.log('📦 Installing worker dependencies...')

  const nodeModulesDir = join(__dirname, 'node_modules')
  const rootNodeModulesDir = join(__dirname, '../../node_modules')

  // 确保 node_modules 目录存在
  if (!existsSync(nodeModulesDir)) {
    mkdirSync(nodeModulesDir, { recursive: true })
  }

  // 读取当前 package.json 获取依赖列表
  const packageJsonPath = join(__dirname, 'package.json')
  const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf8'))
  const dependencies = Object.keys(packageJson.dependencies || {})

  console.log(`📋 Dependencies to copy: ${dependencies.join(', ')}`)

  // 复制每个依赖及其子依赖
  const copiedPackages = new Set()
  for (const depName of dependencies) {
    copyPackageWithDependencies(depName, rootNodeModulesDir, nodeModulesDir, copiedPackages)
  }

  console.log(`✅ Copied ${copiedPackages.size} packages total`)
}

try {
  const distDir = join(__dirname, 'dist')
  if (!existsSync(distDir)) {
    mkdirSync(distDir, { recursive: true })
  }

  // 首先安装依赖
  installWorkerDependencies()

  console.log('📄 Copying compiled worker files...')
  const sourceWorkerPath = join(__dirname, '../main/dist/upload-worker.js')
  const targetWorkerPath = join(distDir, 'upload-worker.js')

  if (!existsSync(sourceWorkerPath)) {
    throw new Error(`Compiled worker file not found: ${sourceWorkerPath}. Please run main build first.`)
  }

  copyFileSync(sourceWorkerPath, targetWorkerPath)

  copyFileSync(
    join(__dirname, 'package.json'),
    join(distDir, 'package.json')
  )

  console.log('✅ Upload worker build completed!')
  console.log(`📁 Output: ${distDir}`)
  console.log(`📄 Worker file: ${targetWorkerPath}`)
} catch (error) {
  console.error('❌ Build failed:', error.message)
  process.exit(1)
}
