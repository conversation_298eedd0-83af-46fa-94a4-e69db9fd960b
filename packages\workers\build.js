import { existsSync, mkdirSync, copyFileSync, cpSync, writeFileSync } from 'fs'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

console.log('🔨 Building upload worker...')

/**
 * 复制 Worker 依赖
 */
function copyWorkerDependencies(distDir) {
  console.log('📦 Copying worker dependencies...')

  // Worker 需要的依赖列表
  const deps = [
    'ali-oss',
    'better-sqlite3'
  ]

  const mainNodeModules = join(__dirname, '../../node_modules')
  const workerNodeModules = join(distDir, 'node_modules')

  // 确保目标 node_modules 目录存在
  if (!existsSync(workerNodeModules)) {
    mkdirSync(workerNodeModules, { recursive: true })
  }

  for (const dep of deps) {
    const srcPath = join(mainNodeModules, dep)
    const destPath = join(workerNodeModules, dep)

    if (existsSync(srcPath)) {
      console.log(`📦 Copying ${dep}...`)
      cpSync(srcPath, destPath, { recursive: true, dereference: true })
    } else {
      console.warn(`⚠️ Dependency not found: ${dep}`)
    }
  }

  console.log('✅ Worker dependencies copied successfully')
}

try {
  const distDir = join(__dirname, 'dist')
  if (!existsSync(distDir)) {
    mkdirSync(distDir, { recursive: true })
  }

  // 复制 Worker 依赖到打包目录
  copyWorkerDependencies(distDir)

  console.log('📄 Copying compiled worker files...')
  const sourceWorkerPath = join(__dirname, '../main/dist/upload-worker.js')
  const targetWorkerPath = join(distDir, 'upload-worker.js')

  if (!existsSync(sourceWorkerPath)) {
    throw new Error(`Compiled worker file not found: ${sourceWorkerPath}. Please run main build first.`)
  }

  copyFileSync(sourceWorkerPath, targetWorkerPath)

  copyFileSync(
    join(__dirname, 'package.json'),
    join(distDir, 'package.json')
  )

  console.log('✅ Upload worker build completed!')
  console.log(`📁 Output: ${distDir}`)
  console.log(`📄 Worker file: ${targetWorkerPath}`)
} catch (error) {
  console.error('❌ Build failed:', error.message)
  process.exit(1)
}
